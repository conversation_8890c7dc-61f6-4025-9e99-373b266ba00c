'use server';

import dbConnect from '../mongodb';
import { Types } from 'mongoose';
import { WaveModel, IWaveInput, IWaveDoc } from '../models/wave';
import {
  SurveyModel,
  ISurveyImport,
  ISurveyInput,
} from '../models/survey';
import {
  UserPasscodeModel,
  IUserPasscodeInput,
} from '../models/user-passcode';
import { ResponseModel } from '../models/response';
import { getLatestWaveSurveyDocs } from './surveys';
import generatePasscode from '../../util/generate-passcode';
import { formatDate } from '../../util/format-date';
import { formatWaveLabel } from '../../util/format-wave-label';
import sendSimpleMessages from '../mailgun';

export interface IWaveVm {
  id: string;
  name: string;
  status: string;
  dateInitiated: string;
  surveyCount?: number;
  responseCount?: number;
  displayLabel?: string; // Formatted label with date prefix for dropdowns
}

// Map a Wave from Document to View Model (optimized version without N+1 queries)
export async function fromWaveDocToVm(doc: IWaveDoc, surveyCount?: number, responseCount?: number): Promise<IWaveVm> {
  // If counts are not provided, fetch them (fallback for single wave operations)
  let finalSurveyCount = surveyCount;
  let finalResponseCount = responseCount;

  if (finalSurveyCount === undefined) {
    finalSurveyCount = await SurveyModel.countDocuments({ waveId: doc._id });
  }

  if (finalResponseCount === undefined) {
    // Count responses for this wave by joining with surveys
    const responseCountResult = await ResponseModel.aggregate([
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      {
        $match: {
          'survey.waveId': doc._id
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 }
        }
      }
    ]);

    finalResponseCount = responseCountResult.length > 0 ? responseCountResult[0].count : 0;
  }

  return {
    id: doc._id.toString(),
    name: doc.name,
    status: doc.status,
    dateInitiated: formatDate(doc.createdAt.toString()),
    surveyCount: finalSurveyCount,
    responseCount: finalResponseCount,
    displayLabel: formatWaveLabel(doc.name, doc.createdAt),
  };
}

// Get Wave View Models (optimized to avoid N+1 queries)
export async function getWaveVms(includeSeed: boolean = true): Promise<IWaveVm[]> {
  await dbConnect();

  const filter = includeSeed ? {} : { status: { $ne: 'seed' } };
  const waves: IWaveDoc[] = await WaveModel.find(filter).sort({ createdAt: -1 });

  if (waves.length === 0) {
    return [];
  }

  const waveIds = waves.map(wave => wave._id);

  // Get survey counts for all waves in a single aggregation
  const surveyCounts = await SurveyModel.aggregate([
    { $match: { waveId: { $in: waveIds } } },
    { $group: { _id: '$waveId', count: { $sum: 1 } } }
  ]);

  // Get response counts for all waves in a single aggregation
  const responseCounts = await ResponseModel.aggregate([
    {
      $lookup: {
        from: 'surveys',
        localField: 'surveyId',
        foreignField: '_id',
        as: 'survey'
      }
    },
    { $unwind: '$survey' },
    { $match: { 'survey.waveId': { $in: waveIds } } },
    { $group: { _id: '$survey.waveId', count: { $sum: 1 } } }
  ]);

  // Create lookup maps for O(1) access
  const surveyCountMap = new Map(surveyCounts.map(item => [item._id.toString(), item.count]));
  const responseCountMap = new Map(responseCounts.map(item => [item._id.toString(), item.count]));

  // Convert to view models with pre-fetched counts
  return Promise.all(waves.map(wave =>
    fromWaveDocToVm(
      wave,
      surveyCountMap.get(wave._id.toString()) || 0,
      responseCountMap.get(wave._id.toString()) || 0
    )
  ));
}

// Get Wave View Models for dashboard (excludes seed data)
export async function getDashboardWaveVms(): Promise<IWaveVm[]> {
  return getWaveVms(false);
}

// Get Seed Wave View Models for creating new waves
export async function getSeedWaveVms(): Promise<IWaveVm[]> {
  await dbConnect();

  const data: IWaveDoc[] = await WaveModel.find({ status: 'seed' }).sort({ createdAt: -1 });

  return Promise.all(data.map(wave => fromWaveDocToVm(wave)));
}

// Add a new Wave Document and Survey Documents given a Wave Input and Survey Imports
export async function addWave(
  wave: IWaveInput,
  surveyImports: ISurveyImport[]
): Promise<{ success: boolean; waveId?: string }> {
  await dbConnect();

  if (!wave.name) {
    throw new Error('Name is required');
  }

  const existingWave = await WaveModel.exists({ name: wave.name });
  if (existingWave) {
    throw new Error('Name already exists');
  }

  if (!surveyImports.length) {
    throw new Error('No surveys provided');
  }

  let newWaveId: Types.ObjectId = new Types.ObjectId();

  // Create the wave
  const newWave = await WaveModel.create({
    name: wave.name,
    status: wave.status || 'seed',
  });
  newWaveId = newWave._id;

  try {
    // Create surveys
    const surveyInputs: ISurveyInput[] = surveyImports.map((surveyImport) => ({
      ...surveyImport,
      waveId: newWaveId,
    }));

    await SurveyModel.insertMany(surveyInputs);

    // Only generate passcodes for non-seed waves (actual waves that will send emails)
    if (newWave.status !== 'seed') {
      // Generate passcodes for unique users
      const uniqueUsers = new Map<string, ISurveyImport>();
      surveyImports.forEach(survey => {
        // Only create passcodes for surveys with valid email addresses
        if (survey.userEmail && survey.userEmail.trim() !== '') {
          uniqueUsers.set(survey.userEmail, survey);
        }
      });

      const passcodeInputs: IUserPasscodeInput[] = Array.from(uniqueUsers.values()).map(user => ({
        userEmail: user.userEmail!,
        passcode: generatePasscode(),
        waveId: newWaveId,
        isActive: true,
      }));

      await UserPasscodeModel.insertMany(passcodeInputs);
    }

    // Return success without redirect - let the client handle navigation
    return { success: true, waveId: newWaveId.toString() };

  } catch (err: unknown) {
    // Rollback wave creation if surveys/passcodes fail
    await WaveModel.findByIdAndDelete(newWaveId);
    const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
    return Promise.reject(errorMessage);
  }
}

// Update wave status
export async function updateWaveStatus(
  waveId: string, 
  status: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed'
): Promise<void> {
  await dbConnect();
  
  await WaveModel.findByIdAndUpdate(waveId, { status });
}

// Launch wave (set to active and send emails)
export async function launchWave(waveId: string): Promise<void> {
  await dbConnect();
  
  // Update status to active
  await WaveModel.findByIdAndUpdate(waveId, { status: 'active' });
  
  // Get all surveys for this wave and send emails
  const surveys = await SurveyModel.find({ waveId });
  
  // Send emails
  await sendSimpleMessages(surveys);
}

export async function emailBlastLatestWave(): Promise<undefined> {
  // Get survey docs and send emails
  const surveyDocs = await getLatestWaveSurveyDocs();
  await sendSimpleMessages(surveyDocs);
}

// Get wave statistics for reporting
export async function getWaveStatistics(waveId: string): Promise<{
  waveName: string;
  status: string;
  totalSurveys: number;
  completedSurveys: number;
  averageScore?: number;
}> {
  await dbConnect();

  const wave = await WaveModel.findById(waveId);
  if (!wave) {
    throw new Error(`Wave not found: ${waveId}`);
  }

  const surveys = await SurveyModel.find({ waveId });
  const totalSurveys = surveys.length;

  // Count completed surveys (those with responses)
  const completedSurveys = surveys.filter(survey =>
    survey.responses && survey.responses.length > 0
  ).length;

  // Calculate average score
  const allResponses = surveys.flatMap(survey => survey.responses || []);
  const averageScore = allResponses.length > 0
    ? allResponses.reduce((sum, response) => sum + (response.overallRating || 0), 0) / allResponses.length
    : undefined;

  return {
    waveName: wave.name,
    status: wave.status,
    totalSurveys,
    completedSurveys,
    averageScore
  };
}
