import { Document, Schema, model, models, Types } from 'mongoose';

export interface IWaveConfig {
  selectedRegions: string[];
  selectedAgencyTypes: string[];
  selectedBrands: string[];
  selectedAgencies: string[];
  selectedPeriod: string;
  selectedYear: number;
}

export interface IWaveInput {
  name: string;
  status?: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed';
  config?: IWaveConfig;
}

export interface IWaveDoc extends IWaveInput, Document {
  _id: Types.ObjectId;
  status: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed';
  config?: IWaveConfig;
  createdAt: Date;
  updatedAt: Date;
}

const WaveSchema = new Schema<IWaveDoc>(
  {
    name: { type: String, required: true, unique: true },
    status: {
      type: String,
      enum: ['draft', 'launched', 'active', 'completed', 'archived', 'seed'],
      default: 'draft'
    },
    config: {
      type: Schema.Types.Mixed,
      required: false
    },
  },
  { timestamps: true }
);

// Performance indexes for common queries
WaveSchema.index({ status: 1 }); // For filtering by status (seed vs non-seed)
WaveSchema.index({ createdAt: -1 }); // For sorting by creation date
WaveSchema.index({ status: 1, createdAt: -1 }); // Compound index for status + date queries
// Note: name index is already created by unique: true, so we don't need to add it explicitly

// Clear any existing model to force schema update
if (models.Wave) {
  delete models.Wave;
}

export const WaveModel = model<IWaveDoc>('Wave', WaveSchema);
