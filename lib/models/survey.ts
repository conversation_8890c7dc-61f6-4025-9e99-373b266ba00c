import { Document, Schema, model, models, Types } from 'mongoose';

export interface ISurveyImport {
  accountName: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  userName: string;
  userEmail?: string;
  userStatus: string;
  inScope: string;
  notes?: string;
}

export interface ISurveyInput extends ISurveyImport {
  waveId: Types.ObjectId;
}

export interface ISurveyDoc extends ISurveyInput, Document {
  _id: Types.ObjectId;
  isRemoved?: boolean;
  removedAt?: Date;
  removalReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const SurveySchema = new Schema<ISurveyDoc>(
  {
    waveId: { type: Schema.Types.ObjectId, ref: 'Wave', required: true },
    accountName: { type: String, required: true },
    agencyName: { type: String, required: true },
    agencyType: { type: String, required: true },
    brand: { type: String, required: true },
    country: { type: String, required: true },
    region: { type: String, required: true },
    assessmentType: { type: String, required: true },
    userName: { type: String, required: true },
    userEmail: { type: String, default: '' },
    userStatus: { type: String, required: true },
    inScope: { type: String, required: true },
    notes: { type: String, default: '' },
    isRemoved: { type: Boolean, default: false },
    removedAt: { type: Date },
    removalReason: { type: String },
  },
  { timestamps: true }
);

// Index for finding surveys by user email and wave
SurveySchema.index({ userEmail: 1, waveId: 1 });

// Performance indexes for aggregation queries
SurveySchema.index({ waveId: 1 }); // For wave-based filtering
SurveySchema.index({ agencyName: 1 }); // For agency-based filtering
SurveySchema.index({ brand: 1 }); // For brand-based filtering
SurveySchema.index({ country: 1 }); // For region/country-based filtering
SurveySchema.index({ agencyType: 1 }); // For agency type filtering
SurveySchema.index({ assessmentType: 1 }); // For assessment type filtering
SurveySchema.index({ isRemoved: 1 }); // For filtering removed surveys
SurveySchema.index({ waveId: 1, isRemoved: 1 }); // Compound index for wave + removal status

// Compound indexes for filtered-options aggregation performance
SurveySchema.index({ waveId: 1, isRemoved: 1, agencyName: 1 }); // For agency filtering
SurveySchema.index({ waveId: 1, isRemoved: 1, brand: 1 }); // For brand filtering
SurveySchema.index({ waveId: 1, isRemoved: 1, country: 1 }); // For region filtering
SurveySchema.index({ waveId: 1, isRemoved: 1, agencyType: 1 }); // For agency type filtering

// Compound indexes for common filter combinations
SurveySchema.index({ waveId: 1, agencyName: 1, brand: 1 }); // Agency + brand filtering
SurveySchema.index({ waveId: 1, agencyName: 1, country: 1 }); // Agency + region filtering
SurveySchema.index({ waveId: 1, brand: 1, country: 1 }); // Brand + region filtering

export const SurveyModel = models.Survey || model<ISurveyDoc>('Survey', SurveySchema);
