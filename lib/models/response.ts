import { Document, Schema, model, models, Types } from 'mongoose';

interface IAnswer {
  questionNumber: number;
  score: number;
  comment: string;
}

export interface IResponseImport {
  surveyId: string;
  assessorName: string;
  assessorType: string;
  answers: IAnswer[];
}

export interface IResponseInput {
  surveyId: Types.ObjectId;
  assessorName: string;
  assessorType: string;
  q1Score?: number;
  q1Comment?: string;
  q2Score?: number;
  q2Comment?: string;
  q3Score?: number;
  q3Comment?: string;
  q4Score?: number;
  q4Comment?: string;
  q5Score?: number;
  q5Comment?: string;
  q6Score?: number; // NPS question score (1-10)
  npsScore?: number;
  overallRating?: number;
  waveId?: Types.ObjectId;
  userEmail?: string;
  userName?: string;
}

export interface IResponseDoc extends IResponseInput, Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const ResponseSchema = new Schema<IResponseDoc>(
  {
    surveyId: { type: Schema.Types.ObjectId, ref: 'Survey', required: true },
    assessorName: { type: String, required: true },
    assessorType: { type: String, required: true },
    q1Score: { type: Number, min: 1, max: 5 },
    q1Comment: { type: String, default: '' },
    q2Score: { type: Number, min: 1, max: 5 },
    q2Comment: { type: String, default: '' },
    q3Score: { type: Number, min: 1, max: 5 },
    q3Comment: { type: String, default: '' },
    q4Score: { type: Number, min: 1, max: 5 },
    q4Comment: { type: String, default: '' },
    q5Score: { type: Number, min: 1, max: 5 },
    q5Comment: { type: String, default: '' },
    q6Score: { type: Number, min: 1, max: 10 }, // NPS question score (1-10)
    npsScore: { type: Number },
    overallRating: { type: Number },
    waveId: { type: Schema.Types.ObjectId, ref: 'Wave' },
    userEmail: { type: String },
    userName: { type: String },
  },
  { timestamps: true, strict: false } // Allow dynamic fields for question responses
);

// Index for finding responses by survey
ResponseSchema.index({ surveyId: 1 });

// Index for finding responses by user and survey
ResponseSchema.index({ surveyId: 1, userEmail: 1 });
ResponseSchema.index({ surveyId: 1, userEmail: 1, waveId: 1 });

// Performance indexes for aggregation queries
ResponseSchema.index({ waveId: 1 }); // For wave-based filtering
ResponseSchema.index({ createdAt: -1 }); // For sorting by creation date
ResponseSchema.index({ surveyId: 1, createdAt: -1 }); // Compound index for survey + date queries

// Compound indexes for response stats aggregation performance
ResponseSchema.index({ surveyId: 1, waveId: 1 }); // For joining with surveys and filtering by wave

export const ResponseModel = models.Response || model<IResponseDoc>('Response', ResponseSchema);
