#!/usr/bin/env node

/**
 * Performance test script to verify improvements
 * Run this script to test the optimized API endpoints
 */

const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

async function testEndpoint(name, url, expectedMaxTime = 5000) {
  console.log(`\n🧪 Testing ${name}...`);
  const startTime = Date.now();
  
  try {
    const response = await axios.get(`${BASE_URL}${url}`, {
      timeout: 30000 // 30 second timeout
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const status = duration < expectedMaxTime ? '✅' : '⚠️';
    console.log(`${status} ${name}: ${duration}ms (expected < ${expectedMaxTime}ms)`);
    
    if (response.data) {
      if (Array.isArray(response.data)) {
        console.log(`   📊 Returned ${response.data.length} items`);
      } else if (typeof response.data === 'object') {
        const keys = Object.keys(response.data);
        console.log(`   📊 Returned object with keys: ${keys.join(', ')}`);
      }
    }
    
    return { name, duration, success: true, status: response.status };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`❌ ${name}: FAILED after ${duration}ms`);
    console.log(`   Error: ${error.message}`);
    
    return { name, duration, success: false, error: error.message };
  }
}

async function runPerformanceTests() {
  console.log('🚀 Starting Performance Tests');
  console.log(`📍 Base URL: ${BASE_URL}`);
  
  const tests = [
    { name: 'Get Waves', url: '/api/waves?excludeSeed=true', maxTime: 2000 },
    { name: 'Get Unique Agencies', url: '/api/surveys/unique-agencies', maxTime: 1000 },
    { name: 'Get Unique Brands', url: '/api/surveys/unique-brands', maxTime: 1000 },
    { name: 'Get Unique Regions', url: '/api/surveys/unique-regions', maxTime: 1000 },
    { name: 'Get All Unique Values (New)', url: '/api/surveys/all-unique-values', maxTime: 1500 },
    { name: 'Get Filtered Options', url: '/api/surveys/filtered-options', maxTime: 2000 },
    { name: 'Get Response Stats', url: '/api/responses/stats', maxTime: 2000 },
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.name, test.url, test.maxTime);
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Summary
  console.log('\n📊 Performance Test Summary');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    const avgTime = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
    console.log(`⏱️  Average response time: ${Math.round(avgTime)}ms`);
    
    const slowest = successful.reduce((max, r) => r.duration > max.duration ? r : max);
    const fastest = successful.reduce((min, r) => r.duration < min.duration ? r : min);
    
    console.log(`🐌 Slowest: ${slowest.name} (${slowest.duration}ms)`);
    console.log(`⚡ Fastest: ${fastest.name} (${fastest.duration}ms)`);
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Tests:');
    failed.forEach(f => {
      console.log(`   - ${f.name}: ${f.error}`);
    });
  }
  
  console.log('\n🎯 Performance Improvements Applied:');
  console.log('   • Added database indexes for Wave model');
  console.log('   • Optimized Wave API to eliminate N+1 queries');
  console.log('   • Combined unique value queries using $facet aggregation');
  console.log('   • Added caching layer for frequently accessed data');
  console.log('   • Optimized filtered options aggregation pipeline');
  
  process.exit(failed.length > 0 ? 1 : 0);
}

// Run the tests
runPerformanceTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
