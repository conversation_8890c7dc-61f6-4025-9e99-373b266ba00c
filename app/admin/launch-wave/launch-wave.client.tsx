'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import {
	Box,
	Typography,
	Alert,
	CircularProgress,
	Button,
	Divider,
	Chip,
	Select,
	MenuItem,
	InputLabel,
	ToggleButton,
	ToggleButtonGroup,
	TextField,
	Stack,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogContentText,
	DialogActions,
} from '@mui/material';
import DashboardPanel from '../dashboard/DashboardPanel';
import PanelHeader from '../dashboard/PanelHeader';
import { RestartAlt as ResetIcon } from '@mui/icons-material';
import PanelSubheader from '../dashboard/PanelSubheader';
import ElevatedFormControl from '@/app/_components/ElevatedFormControl';
import { IWaveConfig } from '../../../lib/models/wave';

interface SeedData {
	hasData: boolean;
	message?: string;
	waveInfo?: {
		id: string;
		name: string;
		createdAt: string;
	};
	data?: {
		regions: string[];
		agencyTypes: string[];
		brands: string[];
		agencies: string[];
	};
	stats?: {
		totalSurveys: number;
		agencyRespondents: number;
		abiRespondents: number;
	};
}

interface Survey {
	id: string;
	accountName: string;
	agencyName: string;
	agencyType: string;
	brand: string;
	country: string;
	region: string;
	assessmentType: string;
	userName: string;
	userEmail: string;
	userStatus: string;
	inScope: string;
	notes: string;
	waveId: string;
}

interface ExistingWave {
	id: string;
	name: string;
	status: string;
	config?: IWaveConfig;
	createdAt: string;
	updatedAt: string;
}

interface SeedWave {
	id: string;
	name: string;
	status: string;
	dateInitiated: string;
	surveyCount?: number;
}

export default function LaunchWaveClient() {
	const searchParams = useSearchParams();
	const router = useRouter();
	const editWaveId = searchParams.get('edit');
	const isEditing = !!editWaveId;

	const [seedData, setSeedData] = useState<SeedData | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Seed wave selection states
	const [availableSeedWaves, setAvailableSeedWaves] = useState<SeedWave[]>([]);
	const [selectedSeedWaveId, setSelectedSeedWaveId] = useState<string>('');
	const [loadingSeedWaves, setLoadingSeedWaves] = useState(false);

	// Existing wave data for editing
	const [existingWave, setExistingWave] = useState<ExistingWave | null>(null);
	const [loadingWave, setLoadingWave] = useState(false);

	// Selection states
	const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
	const [selectedAgencyTypes, setSelectedAgencyTypes] = useState<string[]>([]);
	const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
	const [selectedAgencies, setSelectedAgencies] = useState<string[]>([]);

	// Form states
	const [waveName, setWaveName] = useState('');
	const [selectedPeriod, setSelectedPeriod] = useState<string>('FY');
	const [selectedYear, setSelectedYear] = useState<number>(
		new Date().getFullYear()
	);
	const [isAutoGenerating, setIsAutoGenerating] = useState(true);

	// Save draft states
	const [isSaving, setIsSaving] = useState(false);
	const [saveSuccess, setSaveSuccess] = useState<string | null>(null);
	const [saveError, setSaveError] = useState<string | null>(null);

	// Launch states
	const [isLaunching, setIsLaunching] = useState(false);

	// Track if wave has been saved (for enabling Launch Emails button)
	const [waveHasBeenSaved, setWaveHasBeenSaved] = useState(false);

	// Delete states
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);

	// Helper function to get surveys from API (we'll need to fetch this)
	const [allSurveys, setAllSurveys] = useState<Survey[]>([]);

	// Flag to prevent auto-updates from triggering more auto-updates
	const isAutoUpdating = useRef(false);

	// Load available seed waves
	useEffect(() => {
		const fetchSeedWaves = async () => {
			try {
				setLoadingSeedWaves(true);
				const response = await fetch('/api/waves/seed');
				if (response.ok) {
					const seedWaves: SeedWave[] = await response.json();
					setAvailableSeedWaves(seedWaves);
					// Set default to the latest (first) seed wave if available
					if (seedWaves.length > 0) {
						setSelectedSeedWaveId(seedWaves[0].id);
					}
				}
			} catch (error) {
				console.error('Error loading seed waves:', error);
			} finally {
				setLoadingSeedWaves(false);
			}
		};

		fetchSeedWaves();
	}, []);

	// Load existing wave for editing
	useEffect(() => {
		if (isEditing && editWaveId) {
			const fetchWave = async () => {
				try {
					setLoadingWave(true);
					const response = await fetch(`/api/waves/${editWaveId}`);
					if (response.ok) {
						const wave: ExistingWave = await response.json();
						setExistingWave(wave);

						// Load wave configuration
						if (wave.config) {
							setSelectedRegions(wave.config.selectedRegions || []);
							setSelectedAgencyTypes(wave.config.selectedAgencyTypes || []);
							setSelectedBrands(wave.config.selectedBrands || []);
							setSelectedAgencies(wave.config.selectedAgencies || []);
							setSelectedPeriod(wave.config.selectedPeriod || 'FY');
							setSelectedYear(
								wave.config.selectedYear || new Date().getFullYear()
							);
						}

						// Set wave name and disable auto-generation since this is an existing wave
						setWaveName(wave.name);
						setIsAutoGenerating(false);

						// Enable launch button for existing waves
						setWaveHasBeenSaved(true);

						// For editing, we still need to load seed data but use the latest seed wave
						// since we need the options for the dropdown selections
						if (availableSeedWaves.length > 0) {
							setSelectedSeedWaveId(availableSeedWaves[0].id);
						}
					} else {
						const errorData = await response.json();
						setError(errorData.error || 'Failed to load wave');
					}
				} catch (error) {
					console.error('Error loading wave:', error);
					setError('Failed to load wave for editing');
				} finally {
					setLoadingWave(false);
				}
			};

			fetchWave();
		}
	}, [isEditing, editWaveId, availableSeedWaves]);

	// Delete wave function
	const handleDeleteWave = async () => {
		if (!existingWave) return;

		try {
			setIsDeleting(true);
			const response = await fetch(`/api/waves/${existingWave.id}`, {
				method: 'DELETE',
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to delete wave');
			}

			// Navigate back to waves page after successful deletion
			router.push('/admin/waves');
		} catch (error) {
			console.error('Error deleting wave:', error);
			setSaveError(
				error instanceof Error ? error.message : 'Failed to delete wave'
			);
			setTimeout(() => setSaveError(null), 10000);
		} finally {
			setIsDeleting(false);
			setShowDeleteDialog(false);
		}
	};

	// Auto-update wave name when selections change (if auto-generating)
	const generateWaveName = useCallback(() => {
		const period = selectedPeriod || 'FY';
		const regions = selectedRegions.length;
		const brands = selectedBrands.length;
		const agencies = selectedAgencies.length;

		// Calculate filtered surveys inline
		const filteredSurveys = allSurveys.filter(
			(survey) =>
				selectedRegions.includes(survey.country) &&
				selectedAgencyTypes.includes(survey.agencyType) &&
				selectedBrands.includes(survey.brand) &&
				selectedAgencies.includes(survey.agencyName)
		);
		const totalSurveys = filteredSurveys.length;

		return `${selectedYear} ${period} - ${regions} Regions - ${brands} Brands - ${agencies} Agencies - ${totalSurveys} Surveys`;
	}, [
		selectedYear,
		selectedPeriod,
		selectedRegions,
		selectedAgencyTypes,
		selectedBrands,
		selectedAgencies,
		allSurveys,
	]);

	useEffect(() => {
		if (isAutoGenerating) {
			setWaveName(generateWaveName());
		}
	}, [
		selectedRegions.length,
		selectedBrands.length,
		selectedAgencies.length,
		selectedPeriod,
		selectedYear,
		isAutoGenerating,
		allSurveys.length,
		generateWaveName,
	]);

	// Fetch surveys when seed data is loaded
	useEffect(() => {
		if (seedData?.hasData && seedData.waveInfo) {
			const fetchSurveys = async () => {
				try {
					const response = await fetch(
						`/api/waves/${seedData.waveInfo!.id}/surveys`
					);
					if (response.ok) {
						const surveys = await response.json();
						setAllSurveys(surveys);
					}
				} catch (error) {
					console.error('Error fetching surveys:', error);
				}
			};
			fetchSurveys();
		}
	}, [seedData]);

	// Filter surveys based on current selections
	const getFilteredSurveys = (
		regions: string[],
		agencyTypes: string[],
		brands: string[],
		agencies: string[]
	) => {
		return allSurveys.filter(
			(survey) =>
				regions.includes(survey.country) &&
				agencyTypes.includes(survey.agencyType) &&
				brands.includes(survey.brand) &&
				agencies.includes(survey.agencyName)
		);
	};

	// Auto-update other categories based on user action (additive or subtractive)
	const autoUpdateSelections = (
		userAction: 'enable' | 'disable',
		changedItem: string,
		changedCategory: 'regions' | 'agencyTypes' | 'brands' | 'agencies',
		newSelectionForChangedCategory: string[]
	) => {
		// Prevent cascading auto-updates
		if (isAutoUpdating.current) return;

		isAutoUpdating.current = true;

		if (userAction === 'enable') {
			// ADDITIVE: Find all surveys that contain the newly enabled item
			let relevantSurveys: Survey[] = [];

			if (changedCategory === 'regions') {
				relevantSurveys = allSurveys.filter((s) => s.country === changedItem);
			} else if (changedCategory === 'agencyTypes') {
				relevantSurveys = allSurveys.filter(
					(s) => s.agencyType === changedItem
				);
			} else if (changedCategory === 'brands') {
				relevantSurveys = allSurveys.filter((s) => s.brand === changedItem);
			} else if (changedCategory === 'agencies') {
				relevantSurveys = allSurveys.filter(
					(s) => s.agencyName === changedItem
				);
			}

			// Get all unique values from surveys containing the newly enabled item
			const additionalRegions = [
				...new Set(relevantSurveys.map((s) => s.country)),
			];
			const additionalAgencyTypes = [
				...new Set(relevantSurveys.map((s) => s.agencyType)),
			];
			const additionalBrands = [
				...new Set(relevantSurveys.map((s) => s.brand)),
			];
			const additionalAgencies = [
				...new Set(relevantSurveys.map((s) => s.agencyName)),
			];

			// Add to existing selections (union)
			if (changedCategory !== 'regions') {
				setSelectedRegions((prev) => [
					...new Set([...prev, ...additionalRegions]),
				]);
			}
			if (changedCategory !== 'agencyTypes') {
				setSelectedAgencyTypes((prev) => [
					...new Set([...prev, ...additionalAgencyTypes]),
				]);
			}
			if (changedCategory !== 'brands') {
				setSelectedBrands((prev) => [
					...new Set([...prev, ...additionalBrands]),
				]);
			}
			if (changedCategory !== 'agencies') {
				setSelectedAgencies((prev) => [
					...new Set([...prev, ...additionalAgencies]),
				]);
			}
		} else {
			// SUBTRACTIVE: Find surveys that match REMAINING selections
			const currentRegions =
				changedCategory === 'regions'
					? newSelectionForChangedCategory
					: selectedRegions;
			const currentAgencyTypes =
				changedCategory === 'agencyTypes'
					? newSelectionForChangedCategory
					: selectedAgencyTypes;
			const currentBrands =
				changedCategory === 'brands'
					? newSelectionForChangedCategory
					: selectedBrands;
			const currentAgencies =
				changedCategory === 'agencies'
					? newSelectionForChangedCategory
					: selectedAgencies;

			const remainingSurveys = getFilteredSurveys(
				currentRegions,
				currentAgencyTypes,
				currentBrands,
				currentAgencies
			);

			// Only keep values that appear in remaining surveys
			const validRegions = [...new Set(remainingSurveys.map((s) => s.country))];
			const validAgencyTypes = [
				...new Set(remainingSurveys.map((s) => s.agencyType)),
			];
			const validBrands = [...new Set(remainingSurveys.map((s) => s.brand))];
			const validAgencies = [
				...new Set(remainingSurveys.map((s) => s.agencyName)),
			];

			if (changedCategory !== 'regions') {
				setSelectedRegions(validRegions);
			}
			if (changedCategory !== 'agencyTypes') {
				setSelectedAgencyTypes(validAgencyTypes);
			}
			if (changedCategory !== 'brands') {
				setSelectedBrands(validBrands);
			}
			if (changedCategory !== 'agencies') {
				setSelectedAgencies(validAgencies);
			}
		}

		// Reset flag after state updates are queued
		setTimeout(() => {
			isAutoUpdating.current = false;
		}, 0);
	};

	// Enhanced toggle function
	const enhancedToggleSelection = (
		item: string,
		selected: string[],
		setSelected: (v: string[]) => void,
		category: 'regions' | 'agencyTypes' | 'brands' | 'agencies'
	) => {
		// Only process user actions, not auto-updates
		if (isAutoUpdating.current) return;

		const wasSelected = selected.includes(item);
		const newSelection = wasSelected
			? selected.filter((i) => i !== item)
			: [...selected, item];

		setSelected(newSelection);

		// Determine if this is an enable or disable action
		const userAction = wasSelected ? 'disable' : 'enable';

		// Trigger auto-updates based on user action
		autoUpdateSelections(userAction, item, category, newSelection);
	};

	// Calculate current stats
	const getCurrentStats = () => {
		const filteredSurveys = getFilteredSurveys(
			selectedRegions,
			selectedAgencyTypes,
			selectedBrands,
			selectedAgencies
		);
		const agencyRespondents = filteredSurveys.filter(
			(s) => s.assessmentType === 'Agency-on-Anheuser Busch In-Bev'
		).length;
		const abiRespondents = filteredSurveys.filter(
			(s) => s.assessmentType === 'Anheuser Busch In-Bev-on-Agency'
		).length;

		// Calculate unique emails that will receive invitations
		const uniqueEmails = new Set(
			filteredSurveys
				.map((s) => s.userEmail)
				.filter((email) => email && email.trim())
		);

		return {
			totalSurveys: filteredSurveys.length,
			agencyRespondents,
			abiRespondents,
			regionCount: selectedRegions.length,
			agencyTypeCount: selectedAgencyTypes.length,
			brandCount: selectedBrands.length,
			agencyCount: selectedAgencies.length,
			uniqueEmailCount: uniqueEmails.size,
		};
	};

	const currentStats = getCurrentStats();

	// Global select all/none functions
	const globalSelectAll = () => {
		if (isAutoUpdating.current) return;

		setSelectedRegions(data!.regions);
		setSelectedAgencyTypes(data!.agencyTypes);
		setSelectedBrands(data!.brands);
		setSelectedAgencies(data!.agencies);
	};

	const globalSelectNone = () => {
		if (isAutoUpdating.current) return;

		setSelectedRegions([]);
		setSelectedAgencyTypes([]);
		setSelectedBrands([]);
		setSelectedAgencies([]);
	};

	// Save as draft function
	const handleSaveAsDraft = async () => {
		try {
			setIsSaving(true);
			setSaveError(null);
			setSaveSuccess(null);

			const response = await fetch('/api/launch-wave/save-draft', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					name: waveName,
					selectedRegions,
					selectedAgencyTypes,
					selectedBrands,
					selectedAgencies,
					selectedPeriod,
					selectedYear,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to save draft');
			}

			setSaveSuccess(result.message);
			// Enable launch button after successful save
			setWaveHasBeenSaved(true);

			// If this was a new wave (not editing), redirect to edit mode
			if (!isEditing && result.waveId) {
				// Navigate to edit mode with the new wave ID
				router.push(`/admin/launch-wave?edit=${result.waveId}`);
			}

			// Clear success message after 5 seconds
			setTimeout(() => setSaveSuccess(null), 5000);
		} catch (error) {
			console.error('Error saving draft:', error);
			setSaveError(
				error instanceof Error ? error.message : 'Failed to save draft'
			);
			// Clear error message after 10 seconds
			setTimeout(() => setSaveError(null), 10000);
		} finally {
			setIsSaving(false);
		}
	};

	// Launch emails function
	const handleLaunchEmails = async () => {
		if (!waveHasBeenSaved) return;

		try {
			setIsLaunching(true);
			setSaveError(null);
			setSaveSuccess(null);

			const waveId = isEditing ? editWaveId : null;

			const response = await fetch('/api/launch-wave/launch-emails', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					waveId,
					name: waveName,
					selectedSeedWaveId,
					selectedRegions,
					selectedAgencyTypes,
					selectedBrands,
					selectedAgencies,
					selectedPeriod,
					selectedYear,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to launch emails');
			}

			setSaveSuccess(result.message);

			// Navigate to waves page after successful launch
			setTimeout(() => {
				router.push('/admin/waves');
			}, 2000);
		} catch (error) {
			console.error('Error launching emails:', error);
			setSaveError(
				error instanceof Error ? error.message : 'Failed to launch emails'
			);
			setTimeout(() => setSaveError(null), 10000);
		} finally {
			setIsLaunching(false);
		}
	};

	// Fetch seed data when selectedSeedWaveId changes
	useEffect(() => {
		if (!selectedSeedWaveId) return; // Wait for seed wave to be selected

		const fetchSeedData = async () => {
			try {
				setLoading(true);
				setError(null);

				const url = selectedSeedWaveId
					? `/api/launch-wave/seed-data?seedWaveId=${selectedSeedWaveId}`
					: '/api/launch-wave/seed-data';
				const response = await fetch(url);
				const data: SeedData = await response.json();

				if (!response.ok) {
					throw new Error(data.message || 'Failed to fetch seed data');
				}

				setSeedData(data);

				// If we have data and we're NOT editing, initialize selections with no items selected
				if (data.hasData && data.data && !isEditing) {
					setSelectedRegions([]);
					setSelectedAgencyTypes([]);
					setSelectedBrands([]);
					setSelectedAgencies([]);

					// Set default period and auto-generate initial wave name
					setSelectedPeriod('FY');
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		fetchSeedData();
	}, [selectedSeedWaveId, isEditing]);

	// Loading state
	if (
		loading ||
		loadingSeedWaves ||
		(isEditing && loadingWave) ||
		(!isEditing && !selectedSeedWaveId)
	) {
		return (
			<Box>
				<Typography
					variant="h4"
					fontWeight={700}
					color="text.primary"
					gutterBottom
				>
					{isEditing ? 'Edit Wave' : 'Launch New Wave'}
				</Typography>
				<DashboardPanel
					sx={{
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center',
						minHeight: 300,
					}}
				>
					<Stack direction="row" spacing={2} alignItems="center">
						<CircularProgress />
						<Typography color="text.secondary">
							{isEditing
								? 'Loading wave...'
								: loadingSeedWaves
									? 'Loading seed waves...'
									: 'Loading seed data...'}
						</Typography>
					</Stack>
				</DashboardPanel>
			</Box>
		);
	}

	// Error state - handle case where we're editing but seed data failed to load
	if (error && !isEditing) {
		return (
			<Box>
				<Typography
					variant="h4"
					fontWeight={700}
					color="text.primary"
					gutterBottom
				>
					{isEditing ? 'Edit Wave' : 'Launch New Wave'}
				</Typography>
				<Alert severity="error" sx={{ mt: 3 }}>
					{error}
				</Alert>
			</Box>
		);
	}

	// No seed data available - but allow editing of existing waves
	if (!seedData?.hasData && !isEditing) {
		return (
			<Box>
				<Typography
					variant="h4"
					fontWeight={700}
					color="text.primary"
					gutterBottom
				>
					{isEditing ? 'Edit Wave' : 'Launch New Wave'}
				</Typography>
				<Alert severity="warning" sx={{ mt: 3 }}>
					{seedData?.message ||
						'No seed data available. Please import seed data first.'}
				</Alert>
			</Box>
		);
	}

	// For editing, we need seed data to be available for the selection options
	if (isEditing && !seedData?.hasData) {
		return (
			<Box>
				<Typography
					variant="h4"
					fontWeight={700}
					color="text.primary"
					gutterBottom
				>
					{isEditing ? 'Edit Wave' : 'Launch New Wave'}
				</Typography>
				<Alert severity="error" sx={{ mt: 3 }}>
					Cannot edit wave: No seed data available for selection options.
				</Alert>
			</Box>
		);
	}

	const { data } = seedData || { data: null };

	return (
		<Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
			{/* Page Title */}
			<Typography variant="h4" fontWeight={700} color="text.primary">
				{isEditing ? 'Edit Wave' : 'Launch New Wave'}
			</Typography>

			{/* Existing Wave Info for Editing */}
			{isEditing && existingWave && (
				<Alert severity="info">
					<Typography variant="body2">
						Editing draft wave: <strong>{existingWave.name}</strong> (created{' '}
						{new Date(existingWave.createdAt).toLocaleDateString()})
					</Typography>
				</Alert>
			)}

			{/* Save Success/Error Messages */}
			{saveSuccess && (
				<Alert severity="success" onClose={() => setSaveSuccess(null)}>
					{saveSuccess}
				</Alert>
			)}
			{saveError && (
				<Alert severity="error" onClose={() => setSaveError(null)}>
					{saveError}
				</Alert>
			)}

			{/* Configuration and Stats Row */}
			<Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
				{/* Wave Configuration Section */}
				<Box sx={{ flex: 1 }}>
					<DashboardPanel
						sx={{
							display: 'flex',
							flexDirection: 'column',
							gap: 2.5,
							justifyContent: 'space-between',
						}}
					>
						<PanelHeader>Wave Configuration</PanelHeader>

						{/* Seed Data Selection */}
						<Box>
							<ElevatedFormControl
								fullWidth
								size="small"
								disabled={loadingSeedWaves}
							>
								<InputLabel id="seed-wave-select-label">
									Select Seed Wave
								</InputLabel>
								<Select
									labelId="seed-wave-select-label"
									value={selectedSeedWaveId}
									label="Select Seed Wave"
									onChange={(event) =>
										setSelectedSeedWaveId(event.target.value)
									}
								>
									{availableSeedWaves.map((seedWave) => (
										<MenuItem key={seedWave.id} value={seedWave.id}>
											{seedWave.name} ({seedWave.surveyCount} surveys) -{' '}
											{seedWave.dateInitiated}
										</MenuItem>
									))}
								</Select>
							</ElevatedFormControl>
						</Box>

						<Box sx={{ display: 'flex', gap: 3, alignItems: 'start' }}>
							<Box>
								<PanelSubheader gutterBottom>Period</PanelSubheader>
								<ToggleButtonGroup
									exclusive
									value={selectedPeriod}
									onChange={(event, newValue) => {
										if (newValue !== null) {
											setSelectedPeriod(newValue);
										}
									}}
									size="small"
								>
									<ToggleButton value="Q1">Q1</ToggleButton>
									<ToggleButton value="Q2">Q2</ToggleButton>
									<ToggleButton value="Q3">Q3</ToggleButton>
									<ToggleButton value="Q4">Q4</ToggleButton>
									<ToggleButton value="H1">H1</ToggleButton>
									<ToggleButton value="H2">H2</ToggleButton>
									<ToggleButton value="FY">FY</ToggleButton>
								</ToggleButtonGroup>
							</Box>

							<Box sx={{ minWidth: 120 }}>
								<PanelSubheader gutterBottom>Year</PanelSubheader>
								<ElevatedFormControl size="small" fullWidth>
									<Select
										value={selectedYear}
										onChange={(event) =>
											setSelectedYear(Number(event.target.value))
										}
									>
										{Array.from(
											{ length: 11 },
											(_, i) => new Date().getFullYear() - 5 + i
										).map((year) => (
											<MenuItem key={year} value={year}>
												{year}
											</MenuItem>
										))}
									</Select>
								</ElevatedFormControl>
							</Box>
						</Box>

						<Box sx={{ mt: 1 }}>
							<TextField
								label="Wave Name"
								fullWidth
								size="small"
								value={waveName}
								onChange={(e) => setWaveName(e.target.value)}
								placeholder="Enter wave name"
								variant="outlined"
								helperText={
									isAutoGenerating
										? 'Auto-generated based on selections'
										: 'Custom wave name'
								}
								InputProps={{
									readOnly: isAutoGenerating,
									sx: isAutoGenerating
										? { backgroundColor: 'action.hover' }
										: {},
									endAdornment: !isEditing ? (
										<Button
											size="small"
											variant={isAutoGenerating ? 'outlined' : 'contained'}
											onClick={() => {
												if (isAutoGenerating) {
													setIsAutoGenerating(false);
												} else {
													setIsAutoGenerating(true);
													setWaveName(generateWaveName());
												}
											}}
											sx={{ minWidth: 'auto', px: 1 }}
										>
											{isAutoGenerating ? 'Edit' : 'Auto'}
										</Button>
									) : null,
								}}
							/>
						</Box>

						<Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
							<ElevatedFormControl>
								<Button
									variant="contained"
									size="small"
									disabled={!waveName.trim() || isSaving}
									onClick={handleSaveAsDraft}
									startIcon={isSaving ? <CircularProgress size={16} /> : null}
								>
									{isSaving
										? 'Saving...'
										: isEditing
											? 'Update Draft'
											: 'Save as Draft'}
								</Button>
							</ElevatedFormControl>

							{isEditing && existingWave && (
								<ElevatedFormControl>
									<Button
										variant="outlined"
										size="small"
										color="error"
										onClick={() => setShowDeleteDialog(true)}
										disabled={isSaving || isDeleting}
										sx={{
											backgroundColor: 'background.paper',
											outline: '1px solid',
											outlineColor: 'error.main',
											'&:hover': {
												outlineColor: 'error.dark',
											},
										}}
									>
										Delete Draft
									</Button>
								</ElevatedFormControl>
							)}

							<ElevatedFormControl>
								<Button
									variant="contained"
									size="small"
									color="secondary"
									disabled={!waveHasBeenSaved || isLaunching || isSaving}
									onClick={handleLaunchEmails}
									startIcon={
										isLaunching ? <CircularProgress size={16} /> : null
									}
								>
									{isLaunching
										? 'Launching...'
										: `Launch ${currentStats.uniqueEmailCount.toLocaleString()} Email${currentStats.uniqueEmailCount === 1 ? '' : 's'
										}`}
								</Button>
							</ElevatedFormControl>
						</Box>
					</DashboardPanel>
				</Box>

				{/* Selection Stats */}
				<Box sx={{ flex: 1 }}>
					<DashboardPanel>
						<PanelHeader gutterBottom>Your Selection Stats</PanelHeader>
						<Stack spacing={1.5}>
							{/* Selection Counts by Dimension */}
							<Box>
								<Typography variant="body2" color="text.secondary">
									<strong>{selectedRegions.length}</strong> of{' '}
									<strong>{data!.regions.length}</strong> Regions selected
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>{selectedAgencyTypes.length}</strong> of{' '}
									<strong>{data!.agencyTypes.length}</strong> Agency Types
									selected
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>{selectedBrands.length}</strong> of{' '}
									<strong>{data!.brands.length}</strong> Brands selected
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>{selectedAgencies.length}</strong> of{' '}
									<strong>{data!.agencies.length}</strong> Agencies selected
								</Typography>
							</Box>

							<Divider />

							{/* Global Selection Controls */}
							<Box>
								<Stack direction="row" spacing={1} justifyContent="center">
									<Button
										size="small"
										variant="outlined"
										onClick={globalSelectAll}
										disabled={!data}
									>
										Select All
									</Button>

									<Button
										variant="text"
										size="small"
										startIcon={<ResetIcon />}
										onClick={globalSelectNone}
										disabled={!data}
										sx={{ minWidth: 'auto', px: 2, color: 'text.secondary' }}
									>
										Reset Wave
									</Button>
								</Stack>
							</Box>

							<Divider />

							{/* Survey Totals */}
							<Box>
								<Typography variant="body2" color="text.secondary">
									<strong>
										{currentStats.agencyRespondents.toLocaleString()}
									</strong>{' '}
									Agency Respondents
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>
										{currentStats.abiRespondents.toLocaleString()}
									</strong>{' '}
									AB-InBev Respondents
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>{currentStats.totalSurveys.toLocaleString()}</strong>{' '}
									Total Surveys
								</Typography>
								<Typography variant="body2" color="text.secondary">
									<strong>
										{currentStats.uniqueEmailCount.toLocaleString()}
									</strong>{' '}
									Unique Email Recipients
								</Typography>
							</Box>
						</Stack>
					</DashboardPanel>
				</Box>
			</Box>

			{/* Selection Filters Row */}
			<Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
				{/* Region */}
				<Box sx={{ flex: 1, minWidth: 300 }}>
					<DashboardPanel>
						<PanelHeader gutterBottom>Region</PanelHeader>
						<PanelSubheader gutterBottom>
							{selectedRegions.length} of {data!.regions.length} selected
						</PanelSubheader>
						<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
							{data!.regions.map((region) => (
								<Chip
									key={region}
									label={region}
									variant="outlined"
									// color="primary"
									clickable
									onClick={() =>
										enhancedToggleSelection(
											region,
											selectedRegions,
											setSelectedRegions,
											'regions'
										)
									}
									size="small"
									sx={{
										fontSize: '0.75rem',
										height: 28,
										'& .MuiChip-label': { px: 1 },
										backgroundColor: selectedRegions.includes(region)
											? 'background.paper'
											: 'action.disabledBackground',
										color: selectedRegions.includes(region)
											? 'primary.main'
											: 'text.disabled',
										borderColor: selectedRegions.includes(region)
											? 'primary.main'
											: 'transparent',
										'&:hover': {
											backgroundColor: selectedRegions.includes(region)
												? 'background.paper'
												: 'action.hover',
											borderColor: 'divider',
										},
									}}
								/>
							))}
						</Box>
					</DashboardPanel>
				</Box>

				{/* Agency Type */}
				<Box sx={{ flex: 1, minWidth: 300 }}>
					<DashboardPanel>
						<PanelHeader gutterBottom>Agency Type</PanelHeader>
						<PanelSubheader gutterBottom>
							{selectedAgencyTypes.length} of {data!.agencyTypes.length}{' '}
							selected
						</PanelSubheader>
						<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
							{data!.agencyTypes.map((type) => (
								<Chip
									key={type}
									label={type}
									variant="outlined"
									color="default"
									clickable
									onClick={() =>
										enhancedToggleSelection(
											type,
											selectedAgencyTypes,
											setSelectedAgencyTypes,
											'agencyTypes'
										)
									}
									size="small"
									sx={{
										fontSize: '0.75rem',
										height: 28,
										'& .MuiChip-label': { px: 1 },
										backgroundColor: selectedAgencyTypes.includes(type)
											? 'background.paper'
											: 'action.disabledBackground',
										color: selectedAgencyTypes.includes(type)
											? 'primary.main'
											: 'text.disabled',
										borderColor: selectedAgencyTypes.includes(type)
											? 'primary.main'
											: 'transparent',
										'&:hover': {
											backgroundColor: selectedAgencyTypes.includes(type)
												? 'background.paper'
												: 'action.hover',
											borderColor: 'divider',
										},
									}}
								/>
							))}
						</Box>
					</DashboardPanel>
				</Box>
			</Box>

			{/* Brands and Agencies Row */}
			<Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
				{/* Brands Selection */}
				<Box sx={{ flex: 1, minWidth: 300 }}>
					<DashboardPanel>
						<PanelHeader gutterBottom>Brand(s)</PanelHeader>
						<PanelSubheader gutterBottom>
							{selectedBrands.length} of {data!.brands.length} selected
						</PanelSubheader>
						<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
							{data!.brands.map((brand) => (
								<Chip
									key={brand}
									label={brand}
									variant="outlined"
									color="default"
									clickable
									onClick={() =>
										enhancedToggleSelection(
											brand,
											selectedBrands,
											setSelectedBrands,
											'brands'
										)
									}
									size="small"
									sx={{
										fontSize: '0.75rem',
										height: 28,
										'& .MuiChip-label': { px: 1 },
										backgroundColor: selectedBrands.includes(brand)
											? 'background.paper'
											: 'action.disabledBackground',
										color: selectedBrands.includes(brand)
											? 'primary.main'
											: 'text.disabled',
										borderColor: selectedBrands.includes(brand)
											? 'primary.main'
											: 'transparent',
										'&:hover': {
											backgroundColor: selectedBrands.includes(brand)
												? 'background.paper'
												: 'action.hover',
											borderColor: 'divider',
										},
									}}
								/>
							))}
						</Box>
					</DashboardPanel>
				</Box>

				{/* Agencies Selection */}
				<Box sx={{ flex: 1, minWidth: 300 }}>
					<DashboardPanel>
						<PanelHeader gutterBottom>Agency(s)</PanelHeader>
						<PanelSubheader gutterBottom>
							{selectedAgencies.length} of {data!.agencies.length} selected
						</PanelSubheader>
						<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
							{data!.agencies.map((agency) => (
								<Chip
									key={agency}
									label={agency}
									variant="outlined"
									color="default"
									clickable
									onClick={() =>
										enhancedToggleSelection(
											agency,
											selectedAgencies,
											setSelectedAgencies,
											'agencies'
										)
									}
									size="small"
									sx={{
										fontSize: '0.75rem',
										height: 28,
										'& .MuiChip-label': { px: 1 },
										backgroundColor: selectedAgencies.includes(agency)
											? 'background.paper'
											: 'action.disabledBackground',
										color: selectedAgencies.includes(agency)
											? 'primary.main'
											: 'text.disabled',
										borderColor: selectedAgencies.includes(agency)
											? 'primary.main'
											: 'transparent',
										'&:hover': {
											backgroundColor: selectedAgencies.includes(agency)
												? 'background.paper'
												: 'action.hover',
											borderColor: 'divider',
										},
									}}
								/>
							))}
						</Box>
					</DashboardPanel>
				</Box>
			</Box>

			{/* Delete Confirmation Dialog */}
			<Dialog
				open={showDeleteDialog}
				onClose={() => setShowDeleteDialog(false)}
			>
				<DialogTitle>Delete Draft Wave</DialogTitle>
				<DialogContent>
					<DialogContentText>
						Are you sure you want to delete the draft wave &quot;
						{existingWave?.name}&quot;? This action cannot be undone.
					</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={() => setShowDeleteDialog(false)}>Cancel</Button>
					<Button
						onClick={handleDeleteWave}
						color="error"
						disabled={isDeleting}
						startIcon={isDeleting ? <CircularProgress size={16} /> : null}
					>
						{isDeleting ? 'Deleting...' : 'Delete'}
					</Button>
				</DialogActions>
			</Dialog>
		</Box>
	);
}
