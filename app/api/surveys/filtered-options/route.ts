import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { SurveyModel } from '../../../../lib/models/survey';
import { monitorDbOperation } from '../../../../lib/performance';
import { cache } from '../../../../lib/cache';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);

    // Get current filter selections
    const selectedAgency = searchParams.get('agency');
    const selectedBrand = searchParams.get('brand');
    const selectedRegion = searchParams.get('region');
    const selectedWave = searchParams.get('wave');

    // Create cache key based on filter selections
    const cacheKey = cache.generateKey('filtered-options', {
      agency: selectedAgency || 'none',
      brand: selectedBrand || 'none',
      region: selectedRegion || 'none',
      wave: selectedWave || 'none'
    });

    // Try to get from cache first
    const cached = cache.get<{
      agencies: string[];
      brands: string[];
      regions: string[];
      agencyTypes: string[];
    }>(cacheKey);

    if (cached) {
      return NextResponse.json(cached);
    }
    
    // Build base aggregation pipeline to exclude seed waves
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const basePipeline: any[] = [
      {
        $lookup: {
          from: 'waves',
          localField: 'waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      {
        $match: {
          'wave.status': { $ne: 'seed' },
          'isRemoved': { $ne: true }
        }
      }
    ];
    
    // Add wave filter if specified
    if (selectedWave) {
      const waveNames = selectedWave.split(',').map(name => name.trim());
      basePipeline.push({
        $match: {
          'wave.name': { $in: waveNames }
        }
      });
    }
    
    // Get available agencies (filter by brand and region, but NOT by agency)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const agencyMatchConditions: any = {};
    if (selectedBrand && selectedBrand !== 'All Brands') {
      agencyMatchConditions.brand = selectedBrand;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      agencyMatchConditions.country = selectedRegion;
    }
    
    const agenciesPipeline = [...basePipeline];
    if (Object.keys(agencyMatchConditions).length > 0) {
      agenciesPipeline.push({ $match: agencyMatchConditions });
    }
    agenciesPipeline.push(
      { $group: { _id: '$agencyName' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available brands (filter by agency and region, but NOT by brand)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const brandMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      brandMatchConditions.agencyName = selectedAgency;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      brandMatchConditions.country = selectedRegion;
    }
    
    const brandsPipeline = [...basePipeline];
    if (Object.keys(brandMatchConditions).length > 0) {
      brandsPipeline.push({ $match: brandMatchConditions });
    }
    brandsPipeline.push(
      { $group: { _id: '$brand' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available regions (filter by agency and brand, but NOT by region)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const regionMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      regionMatchConditions.agencyName = selectedAgency;
    }
    if (selectedBrand && selectedBrand !== 'All Brands') {
      regionMatchConditions.brand = selectedBrand;
    }
    
    const regionsPipeline = [...basePipeline];
    if (Object.keys(regionMatchConditions).length > 0) {
      regionsPipeline.push({ $match: regionMatchConditions });
    }
    regionsPipeline.push(
      { $group: { _id: '$country' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available agency types (filter by agency, brand, and region, but NOT by agency type)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const agencyTypeMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      agencyTypeMatchConditions.agencyName = selectedAgency;
    }
    if (selectedBrand && selectedBrand !== 'All Brands') {
      agencyTypeMatchConditions.brand = selectedBrand;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      agencyTypeMatchConditions.country = selectedRegion;
    }

    const agencyTypesPipeline = [...basePipeline];
    if (Object.keys(agencyTypeMatchConditions).length > 0) {
      agencyTypesPipeline.push({ $match: agencyTypeMatchConditions });
    }
    agencyTypesPipeline.push(
      { $group: { _id: '$agencyType' } },
      { $sort: { _id: 1 as const } }
    );

    // Optimized: Use a single aggregation to get all unique values with filters
    const combinedResult = await monitorDbOperation(
      'filtered-options-aggregation',
      () => SurveyModel.aggregate([
        ...basePipeline,
        {
          $facet: {
            agencies: [
              ...(Object.keys(agencyMatchConditions).length > 0 ? [{ $match: agencyMatchConditions }] : []),
              { $group: { _id: '$agencyName' } },
              { $sort: { _id: 1 as const } }
            ],
            brands: [
              ...(Object.keys(brandMatchConditions).length > 0 ? [{ $match: brandMatchConditions }] : []),
              { $group: { _id: '$brand' } },
              { $sort: { _id: 1 as const } }
            ],
            regions: [
              ...(Object.keys(regionMatchConditions).length > 0 ? [{ $match: regionMatchConditions }] : []),
              { $group: { _id: '$country' } },
              { $sort: { _id: 1 as const } }
            ],
            agencyTypes: [
              ...(Object.keys(agencyTypeMatchConditions).length > 0 ? [{ $match: agencyTypeMatchConditions }] : []),
              { $group: { _id: '$agencyType' } },
              { $sort: { _id: 1 as const } }
            ]
          }
        }
      ]),
      {
        selectedAgency: !!selectedAgency,
        selectedBrand: !!selectedBrand,
        selectedRegion: !!selectedRegion,
        selectedWave: !!selectedWave
      }
    );

    const result = combinedResult[0] || { agencies: [], brands: [], regions: [], agencyTypes: [] };
    const agenciesResult = result.agencies || [];
    const brandsResult = result.brands || [];
    const regionsResult = result.regions || [];
    const agencyTypesResult = result.agencyTypes || [];

    const agencies = ['All Agencies', ...agenciesResult.map((item: { _id: string }) => item._id)];
    const brands = ['All Brands', ...brandsResult.map((item: { _id: string }) => item._id)];
    const regions = ['All Regions', ...regionsResult.map((item: { _id: string }) => item._id)];
    const agencyTypes = ['All Agency Types', ...agencyTypesResult.map((item: { _id: string }) => item._id)];

    const responseData = {
      agencies,
      brands,
      regions,
      agencyTypes
    };

    // Cache the result for 5 minutes
    cache.set(cacheKey, responseData, 5);

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching filtered options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch filtered options' },
      { status: 500 }
    );
  }
} 