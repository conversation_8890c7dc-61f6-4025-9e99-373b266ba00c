import { NextResponse } from 'next/server';
import { getAllUniqueValues } from '../../../../lib/api/surveys';
import { monitorApiOperation } from '../../../../lib/performance';

export async function GET() {
  try {
    const uniqueValues = await monitorApiOperation(
      'get-all-unique-values',
      () => getAllUniqueValues()
    );
    return NextResponse.json(uniqueValues);
  } catch (error) {
    console.error('Error fetching all unique values:', error);
    return NextResponse.json(
      { error: 'Failed to fetch unique values' },
      { status: 500 }
    );
  }
}
