# .github/workflows/deploy-production.yml
name: Deploy to Production

on:
  push:
    branches: [ "main" ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      NEXT_PUBLIC_API_URL: ${{ secrets.PROD_NEXT_PUBLIC_API_URL }}
      MONGODB_URI: ${{ secrets.PROD_MONGODB_URI }}
      JWT_SECRET: ${{ secrets.PROD_JWT_SECRET }}
      MAILGUN_API_KEY: ${{ secrets.PROD_MAILGUN_API_KEY }}
      MAILGUN_DOMAIN: ${{ secrets.PROD_MAILGUN_DOMAIN }}
      MAILGUN_FROM: ${{ secrets.PROD_MAILGUN_FROM }}
      NODE_ENV: production

    steps:
      - name: 1. Checkout Repository
        uses: actions/checkout@v4

      - name: 2. Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: 3. Install Dependencies
        run: npm ci

      - name: 4. Clear Build Cache
        run: |
          rm -rf .next
          rm -rf node_modules/.cache

      - name: 5. Build Application
        run: npm run build

      - name: 6. Create Deployment Package
        run: |
          # Create a deployment directory
          mkdir -p deploy-package

          # Copy built application
          cp -r .next deploy-package/
          cp -r public deploy-package/

          # Copy configuration files
          cp package.json deploy-package/
          cp package-lock.json deploy-package/
          cp next.config.ts deploy-package/

          # Create a minimal package.json with only production dependencies
          node -e "
            const pkg = require('./package.json');
            const prodPkg = {
              name: pkg.name,
              version: pkg.version,
              private: pkg.private,
              scripts: {
                start: pkg.scripts.start
              },
              dependencies: pkg.dependencies
            };
            require('fs').writeFileSync('deploy-package/package.json', JSON.stringify(prodPkg, null, 2));
          "

      - name: 7. Transfer Files to Server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
          source: "deploy-package/*"
          target: ${{ secrets.PROD_TARGET_PATH }}
          strip_components: 1
          overwrite: true

      - name: 8. Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
          script: |
            cd ${{ secrets.PROD_TARGET_PATH }}

            # Load NVM environment
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

            # Install only production dependencies (much faster)
            npm ci --production --silent

            # Create environment file
            cat > .env << EOF
            MONGODB_URI=${{ secrets.PROD_MONGODB_URI }}
            JWT_SECRET=${{ secrets.PROD_JWT_SECRET }}
            MAILGUN_API_KEY=${{ secrets.PROD_MAILGUN_API_KEY }}
            MAILGUN_DOMAIN=${{ secrets.PROD_MAILGUN_DOMAIN }}
            MAILGUN_FROM=${{ secrets.PROD_MAILGUN_FROM }}
            NODE_ENV=production
            EOF

            # Reload PM2 with zero downtime
            pm2 reload echo360 || pm2 start npm --name "echo360" -- start
