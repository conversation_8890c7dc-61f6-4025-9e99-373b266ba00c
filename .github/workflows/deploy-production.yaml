# .github/workflows/deploy-production.yml
name: Deploy to Production

on:
  push:
    branches: [ "main" ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      NEXT_PUBLIC_API_URL: ${{ secrets.PROD_NEXT_PUBLIC_API_URL }}
      MONGODB_URI: ${{ secrets.PROD_MONGODB_URI }}
      JWT_SECRET: ${{ secrets.PROD_JWT_SECRET }}
      MAILGUN_API_KEY: ${{ secrets.PROD_MAILGUN_API_KEY }}
      MAILGUN_DOMAIN: ${{ secrets.PROD_MAILGUN_DOMAIN }}
      MAILGUN_FROM: ${{ secrets.PROD_MAILGUN_FROM }}
      NODE_ENV: production

    steps:
      - name: 1. Checkout Repository
        uses: actions/checkout@v4

      - name: 2. Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: 3. Install Dependencies
        run: npm ci

      - name: 4. Clear Build Cache
        run: rm -rf .next

      - name: 5. Build Application
        run: npm run build

      - name: 6. Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
          script: |
            # Navigate to your app's directory on the server
            cd ${{ secrets.PROD_TARGET_PATH }}

            # Use rsync to transfer files
            rsync -av --delete ./.next ./.next
            rsync -av --delete ./public/ ./public/
            rsync -av --delete ./package.json ./package.json
            rsync -av --delete ./package-lock.json ./package-lock.json
            rsync -av --delete ./next.config.ts ./next.config.ts

            # Load NVM environment for non-interactive SSH sessions
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

            # Debug: Check what user we're running as and what's in PATH
            echo "Current user: $(whoami)"
            echo "Current PATH: $PATH"
            echo "Node version: $(node --version 2>/dev/null || echo 'Node not found')"
            echo "NPM version: $(npm --version 2>/dev/null || echo 'NPM not found')"
            echo "PM2 version: $(pm2 --version 2>/dev/null || echo 'PM2 not found')"

            # Install all dependencies (Next.js needs TypeScript even in production)
            npm ci

            # Create/update environment file for the application
            cat > .env << EOF
            MONGODB_URI=${{ secrets.PROD_MONGODB_URI }}
            JWT_SECRET=${{ secrets.PROD_JWT_SECRET }}
            MAILGUN_API_KEY=${{ secrets.PROD_MAILGUN_API_KEY }}
            MAILGUN_DOMAIN=${{ secrets.PROD_MAILGUN_DOMAIN }}
            MAILGUN_FROM=${{ secrets.PROD_MAILGUN_FROM }}
            NODE_ENV=production
            EOF

            # Reload the PM2 process for zero-downtime deployment
            pm2 reload echo360
