# .github/workflows/deploy-staging.yml
name: Deploy to Staging

on:
  push:
    branches: [ "staging" ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      NEXT_PUBLIC_API_URL: ${{ secrets.STAGING_NEXT_PUBLIC_API_URL }}
      MONGODB_URI: ${{ secrets.STAGING_MONGODB_URI }}
      JWT_SECRET: ${{ secrets.STAGING_JWT_SECRET }}
      MAILGUN_API_KEY: ${{ secrets.STAGING_MAILGUN_API_KEY }}
      MAILGUN_DOMAIN: ${{ secrets.STAGING_MAILGUN_DOMAIN }}
      MAILGUN_FROM: ${{ secrets.STAGING_MAILGUN_FROM }}
      NODE_ENV: staging

    steps:
      - name: 1. Checkout Repository
        uses: actions/checkout@v4

      - name: 2. Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: 3. Install Dependencies
        run: npm ci

      - name: 4. Clear Build Cache
        run: |
          rm -rf .next
          rm -rf node_modules/.cache

      - name: 5. Run Tests
        run: npm run test

      - name: 6. Build Application
        run: npm run build

      - name: 7. Create Deployment Package
        run: |
          mkdir -p deploy-package
          cp -r .next deploy-package/
          cp -r public deploy-package/
          cp package.json deploy-package/
          cp package-lock.json deploy-package/
          cp next.config.ts deploy-package/

      - name: 8. Transfer Files to Server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          source: "deploy-package/*"
          target: ${{ secrets.STAGING_TARGET_PATH }}
          strip_components: 1
          overwrite: true

      - name: 9. Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          script: |
            cd ${{ secrets.STAGING_TARGET_PATH }}

            # Load NVM environment
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

            # Install production dependencies
            npm ci --production --silent

            # Create environment file
            cat > .env << EOF
            MONGODB_URI=${{ secrets.STAGING_MONGODB_URI }}
            JWT_SECRET=${{ secrets.STAGING_JWT_SECRET }}
            MAILGUN_API_KEY=${{ secrets.STAGING_MAILGUN_API_KEY }}
            MAILGUN_DOMAIN=${{ secrets.STAGING_MAILGUN_DOMAIN }}
            MAILGUN_FROM=${{ secrets.STAGING_MAILGUN_FROM }}
            NODE_ENV=staging
            EOF

            # Reload PM2
            pm2 reload echo360 || pm2 start npm --name "echo360" -- start